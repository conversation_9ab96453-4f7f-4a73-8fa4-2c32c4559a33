'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, X, ShoppingCart } from 'lucide-react';
import userService from '@/services/userService';
import { useNotification } from '@/contexts/NotificationContext';
import { useCart } from '@/contexts/CartContext';
import ToolCard from '@/components/dashboard/ToolCard';

// Filter component
const ToolsFilter = ({
  activeFilter,
  setActiveFilter,
  tools,
}: {
  activeFilter: string;
  setActiveFilter: (filter: string) => void;
  tools: any[];
}) => {
  // Extract unique categories from tools and normalize them
  const uniqueCategories = ["All"];
  tools.forEach((tool) => {
    if (tool.category) {
      let categoryStr = typeof tool.category === 'string'
        ? tool.category
        : tool.category.toString();

      // Capitalize first letter of category for consistent display
      const normalizedCategory = categoryStr.charAt(0).toUpperCase() + categoryStr.slice(1).toLowerCase();
      if (!uniqueCategories.includes(normalizedCategory)) {
        uniqueCategories.push(normalizedCategory);
      }
    }
  });

  // If no tools or categories, use default filters with proper capitalization
  const filters =
    uniqueCategories.length > 1
      ? uniqueCategories
      : ["All", "Streaming", "Productivity", "Design", "AI", "Other"];

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {filters.map((filter) => (
        <button
          key={filter}
          onClick={() => setActiveFilter(filter)}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
            activeFilter === filter
              ? "bg-yellow-400 text-gray-900"
              : "bg-white text-gray-700 hover:bg-gray-100"
          }`}
        >
          {filter}
        </button>
      ))}
    </div>
  );
};

export default function Dashboard() {
  const [activeFilter, setActiveFilter] = useState('All');
  const [tools, setTools] = useState<any[]>([]);
  const [plans, setPlans] = useState<any[]>([]);
  const [isLoadingTools, setIsLoadingTools] = useState<boolean>(true);
  const [isLoadingPlans, setIsLoadingPlans] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const { cartItemCount, openCart, addToCart } = useCart();
  const { showNotification } = useNotification();

  useEffect(() => {
    fetchTools();
    fetchPlans();
  }, []);

  const fetchTools = async () => {
    try {
      setIsLoadingTools(true);
      console.log('Fetching tools in dashboard page...');

      // Fetch real data from the database
      const response = await userService.getTools();

      if (response && response.tools && Array.isArray(response.tools)) {
        console.log('Setting tools state with tool data:', response.tools);
        setTools(response.tools);
        setError(null);
      } else {
        console.error('Invalid response format:', response);
        setError('Failed to load tools: Invalid response format');
        setTools([]);
      }
    } catch (err: any) {
      console.error('Error fetching tools:', err);
      setError(err.message || 'Failed to load tools from the database.');
      setTools([]);
    } finally {
      setIsLoadingTools(false);
    }
  };

  const fetchPlans = async () => {
    try {
      setIsLoadingPlans(true);
      console.log('Fetching plans in dashboard page...');
const plans = await userService.getPlans();
      console.log('Setting plans state with plan data:', plans.plans);
      setPlans(plans.plans);
      setError(null);
    } catch (err: any) {
      console.error('Error setting plans:', err);
      setError('Failed to load plans.');
      setPlans([]);
    } finally {
      setIsLoadingPlans(false);
    }
  };

  // Filter tools based on search term and active filter
  const filteredTools = tools.filter((tool) => {
    // Handle search term matching
    const matchesSearch = searchTerm === '' ||
      (tool.name && tool.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (tool.description && tool.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Handle category filter matching with case insensitivity
    // Also handle potential undefined category and different category formats
    const matchesFilter =
      activeFilter === 'All' ||
      (tool.category &&
        (typeof tool.category === 'string'
          ? tool.category.toLowerCase() === activeFilter.toLowerCase()
          : tool.category.toString().toLowerCase() === activeFilter.toLowerCase()));

    return matchesSearch && matchesFilter;
  });

  return (
    <div>
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-xl p-6 text-white shadow-lg">
          <h2 className="text-2xl font-bold mb-2">
            🚀 Welcome to Partagily!
          </h2>
          <p className="mb-4">
            Access premium international tools and services with ease.
          </p>
        </div>
      </div>

      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">🧩 Available Tools</h2>
        <div className="flex items-center gap-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search tools..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="px-4 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            />
            <button
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400"
              onClick={() => setSearchTerm('')}
            >
              {searchTerm ? <X size={16} /> : <Search size={16} />}
            </button>
          </div>
          <button
            onClick={openCart}
            className="relative bg-yellow-400 hover:bg-yellow-500 text-black font-medium p-2 rounded-lg transition-colors duration-200"
          >
            <ShoppingCart size={20} />
            {cartItemCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                {cartItemCount > 9 ? '9+' : cartItemCount}
              </span>
            )}
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-300 rounded-lg flex items-center">
          <span className="text-xl mr-2">ℹ️</span>
          <p>{error}</p>
        </div>
      )}

      <ToolsFilter
        activeFilter={activeFilter}
        setActiveFilter={setActiveFilter}
        tools={tools}
      />

      {isLoadingTools ? (
        <div className="flex justify-center items-center h-64">
          <motion.div
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "linear",
            }}
            className="w-12 h-12 border-4 border-yellow-400 border-t-transparent rounded-full"
          />
        </div>
      ) : filteredTools.length === 0 ? (
        <div className="text-center py-12 bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md">
          <div className="mb-6">
            <svg
              className="mx-auto h-24 w-24 text-gray-400 dark:text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1"
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
              />
            </svg>
          </div>
          {error ? (
            <>
              <p className="text-gray-500 text-lg font-medium">
                Backend Connection Error
              </p>
              <p className="text-gray-500 mt-2">
                The backend server is not running or is unreachable.
              </p>
              <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-md p-4 max-w-lg mx-auto">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-yellow-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Developer Information
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        To see actual data, please start the backend server. If
                        you're a developer, run:
                      </p>
                      <pre className="mt-2 bg-yellow-100 p-2 rounded text-xs overflow-x-auto">
                        cd partagily-backend
                        <br />
                        npm run start:dev
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">
                No tools found
              </p>
              <p className="text-gray-500 dark:text-gray-400 mt-2">
                Try adjusting your search or filter to find what you're looking for.
              </p>
              <button
                onClick={() => {
                  setActiveFilter('All');
                  setSearchTerm('');
                }}
                className="mt-4 px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium rounded-lg transition-colors"
              >
                Reset Filters
              </button>
            </>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredTools.map((tool) => (
            <ToolCard
              key={tool.id}
              tool={tool}
            />
          ))}
        </div>
      )}

      {/* Plans section removed - now available on the Buy Plan page */}

      {/* Cart is now handled by the CartContext */}
    </div>
  );
}
