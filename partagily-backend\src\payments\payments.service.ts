import { Injectable, Logger, BadRequestException, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { firstValueFrom } from 'rxjs';
import * as crypto from 'crypto';

import { Order, OrderStatus, PaymentMethod } from './entities/order.entity';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentWebhookDto, WebhookEventType } from './dto/payment-webhook.dto';
import { PaymentResponseDto } from './dto/payment-response.dto';

@Injectable()
export class PaymentsService {
  private readonly logger = new Logger(PaymentsService.name);
  private readonly konnectApiUrl: string;
  private readonly konnectApiKey: string;
  private readonly konnectSecretKey: string;
  private readonly webhookSecret: string;
  private readonly frontendUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {
    this.konnectApiUrl = this.configService.get<string>('KONNECT_API_URL', 'https://api.konnect.network/api/v2');
    this.konnectApiKey = this.configService.get<string>('KONNECT_API_KEY', 'test_key');
    this.konnectSecretKey = this.configService.get<string>('KONNECT_SECRET_KEY', 'test_secret');
    this.webhookSecret = this.configService.get<string>('WEBHOOK_SECRET', 'webhook_secret');
    this.frontendUrl = this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000');
  }

  /**
   * Generate a unique order number
   */
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `ORD-${timestamp.substring(timestamp.length - 6)}${random}`;
  }

  /**
   * Create a new payment request with Konnect
   */
  async createPayment(userId: string, createPaymentDto: CreatePaymentDto): Promise<PaymentResponseDto> {
    try {
      // Generate a unique order number
      const orderNumber = this.generateOrderNumber();
      
      // Create a new order record
      const order = this.orderRepository.create({
        orderNumber,
        userId,
        toolId: createPaymentDto.toolId,
        toolName: createPaymentDto.toolName,
        planId: createPaymentDto.planId,
        planName: createPaymentDto.planName,
        amount: createPaymentDto.amount,
        currency: createPaymentDto.currency || 'TND',
        status: OrderStatus.PENDING,
        paymentMethod: createPaymentDto.paymentMethod || PaymentMethod.KONNECT,
        expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      });
      
      const savedOrder = await this.orderRepository.save(order);
      
      // Set return and cancel URLs
      const returnUrl = createPaymentDto.returnUrl || 
        `${this.frontendUrl}/payment/success?orderId=${savedOrder.id}`;
      const cancelUrl = createPaymentDto.cancelUrl || 
        `${this.frontendUrl}/payment/cancel?orderId=${savedOrder.id}`;

      // Prepare the request to Konnect API
      const konnectPayload = {
        amount: createPaymentDto.amount,
        currency: createPaymentDto.currency || 'TND',
        orderId: savedOrder.id,
        orderNumber: orderNumber,
        description: `Payment for ${createPaymentDto.toolName} - ${createPaymentDto.planName}`,
        returnUrl,
        cancelUrl,
        webhookUrl: `${this.configService.get<string>('API_URL', 'http://localhost:3001')}/payments/webhook`,
        metadata: {
          userId,
          toolId: createPaymentDto.toolId,
          planId: createPaymentDto.planId,
        },
      };

      // Make the API request to Konnect
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.konnectApiUrl}/payments`,
          konnectPayload,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.konnectApiKey,
              'Authorization': `Bearer ${this.generateKonnectAuthToken()}`,
            },
          },
        ),
      );

      // Update the order with payment information
      await this.orderRepository.update(savedOrder.id, {
        paymentId: response.data.paymentId,
        paymentUrl: response.data.paymentUrl,
        status: OrderStatus.PROCESSING,
      });

      return {
        success: true,
        message: 'Payment initiated successfully',
        data: {
          orderId: savedOrder.id,
          orderNumber: savedOrder.orderNumber,
          paymentUrl: response.data.paymentUrl,
          status: OrderStatus.PROCESSING,
        },
      };
    } catch (error) {
      this.logger.error('Error creating payment:', error);
      
      if (error.response) {
        this.logger.error('Konnect API error:', error.response.data);
        throw new BadRequestException(error.response.data.message || 'Payment gateway error');
      }
      
      throw new InternalServerErrorException('Failed to process payment request');
    }
  }

  /**
   * Handle webhook notifications from Konnect
   */
  async handleWebhook(payload: PaymentWebhookDto, signature: string): Promise<PaymentResponseDto> {
    try {
      // Verify webhook signature
      this.verifyWebhookSignature(payload, signature);

      // Find the order by payment ID
      const order = await this.orderRepository.findOne({ 
        where: { paymentId: payload.paymentId } 
      });

      if (!order) {
        throw new NotFoundException(`Order with payment ID ${payload.paymentId} not found`);
      }

      // Update order status based on webhook event
      let orderStatus: OrderStatus;
      
      switch (payload.event) {
        case WebhookEventType.PAYMENT_COMPLETED:
          orderStatus = OrderStatus.COMPLETED;
          break;
        case WebhookEventType.PAYMENT_FAILED:
          orderStatus = OrderStatus.FAILED;
          break;
        case WebhookEventType.PAYMENT_REFUNDED:
          orderStatus = OrderStatus.REFUNDED;
          break;
        default:
          orderStatus = OrderStatus.PROCESSING;
      }

      // Update the order
      await this.orderRepository.update(order.id, {
        status: orderStatus,
        transactionId: payload.paymentId,
        updatedAt: new Date(),
      });

      // TODO: If payment is successful, activate the subscription or provide access to the tool

      return {
        success: true,
        message: 'Webhook processed successfully',
        data: {
          orderId: order.id,
          orderNumber: order.orderNumber,
          status: orderStatus,
        },
      };
    } catch (error) {
      this.logger.error('Error processing webhook:', error);
      throw new BadRequestException(error.message || 'Failed to process webhook');
    }
  }

  /**
   * Get order details by ID
   */
  async getOrderById(orderId: string): Promise<Order> {
    const order = await this.orderRepository.findOne({ 
      where: { id: orderId } 
    });

    if (!order) {
      throw new NotFoundException(`Order with ID ${orderId} not found`);
    }

    return order;
  }

  /**
   * Get all orders for a user
   */
  async getUserOrders(userId: string): Promise<Order[]> {
    return this.orderRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Generate authentication token for Konnect API
   */
  private generateKonnectAuthToken(): string {
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const signature = crypto
      .createHmac('sha256', this.konnectSecretKey)
      .update(timestamp)
      .digest('hex');
    
    return Buffer.from(`${timestamp}:${signature}`).toString('base64');
  }

  /**
   * Verify webhook signature from Konnect
   */
  private verifyWebhookSignature(payload: any, signature: string): boolean {
    if (!signature) {
      throw new BadRequestException('Missing webhook signature');
    }

    const expectedSignature = crypto
      .createHmac('sha256', this.webhookSecret)
      .update(JSON.stringify(payload))
      .digest('hex');

    if (signature !== expectedSignature) {
      throw new BadRequestException('Invalid webhook signature');
    }

    return true;
  }
}
