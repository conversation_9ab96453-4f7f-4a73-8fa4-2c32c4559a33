const axios = require('axios');

const API_URL = 'http://localhost:3001';

async function testAuth() {
  try {
    console.log('Testing authentication with admin credentials...');
    
    // Test login with admin credentials
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('Login successful!');
    console.log('Response status:', loginResponse.status);
    console.log('User data:', loginResponse.data.user);
    console.log('Access token:', loginResponse.data.accessToken ? 'Received' : 'Not received');
    console.log('Refresh token:', loginResponse.data.refreshToken ? 'Received' : 'Not received');
    
    // Test with the received token
    if (loginResponse.data.accessToken) {
      console.log('\nTesting /auth/me endpoint with the received token...');
      
      const meResponse = await axios.get(`${API_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${loginResponse.data.accessToken}`
        }
      });
      
      console.log('Auth check successful!');
      console.log('Response status:', meResponse.status);
      console.log('User data:', meResponse.data.user);
    }
    
    return true;
  } catch (error) {
    console.error('Authentication test failed:');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received. Is the server running?');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
    
    return false;
  }
}

// Run the test
testAuth()
  .then(success => {
    if (success) {
      console.log('\nAuthentication test completed successfully!');
    } else {
      console.log('\nAuthentication test failed!');
    }
  })
  .catch(err => {
    console.error('Unexpected error during test:', err);
  });
