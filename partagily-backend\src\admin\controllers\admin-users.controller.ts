import { Controller, Get, Post, Put, Patch, Delete, Param, Body, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../guards/admin.guard';
import { PrismaService } from '../../prisma/prisma.service';
import * as bcrypt from 'bcryptjs';

/**
 * Admin Users Controller
 *
 * This controller provides endpoints to manage users.
 */
@ApiTags('admin-users')
@Controller('admin/users')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminUsersController {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get all users with filtering and pagination
   */
  @Get()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'List of users' })
  @ApiQuery({ name: 'search', required: false })
  @ApiQuery({ name: 'role', required: false })
  @ApiQuery({ name: 'isActive', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'sortBy', required: false })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  async getAllUsers(
    @Query('search') search?: string,
    @Query('role') role?: string,
    @Query('isActive') isActive?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('sortBy') sortBy: string = 'createdAt',
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'desc',
  ) {
    try {
      // Build filter conditions
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (role) {
        where.role = role;
      }

      if (isActive !== undefined) {
        where.isActive = isActive === 'true';
      }

      // Parse pagination parameters
      const pageNum = page ? parseInt(page, 10) : 1;
      const limitNum = limit ? parseInt(limit, 10) : 10;

      // Calculate pagination
      const skip = (pageNum - 1) * limitNum;

      // Build sort options
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      // Get users with pagination
      const users = await this.prisma.user.findMany({
        where,
        skip,
        take: limitNum,
        orderBy,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          avatar: true,
          _count: {
            select: {
              subscriptions: true,
              orders: true,
            },
          },
        },
      });

      // Get total count for pagination
      const total = await this.prisma.user.count({ where });

      return {
        data: users,
        meta: {
          total,
          page: pageNum,
          limit: limitNum,
          totalPages: Math.ceil(total / limitNum),
        },
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Get a user by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiResponse({ status: 200, description: 'User details' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('id') id: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          avatar: true,
          subscriptions: {
            include: {
              plan: true,
            },
          },
          orders: {
            include: {
              tool: true,
              plan: true,
            },
          },
        },
      });

      if (!user) {
        return {
          success: false,
          message: 'User not found',
        };
      }

      return {
        success: true,
        data: user,
      };
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   */
  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  async createUser(@Body() userData: any) {
    try {
      const { name, email, password, role, isActive, emailVerified } = userData;

      // Check if email already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return {
          success: false,
          message: 'Email already exists',
        };
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const newUser = await this.prisma.user.create({
        data: {
          name,
          email,
          password: hashedPassword,
          role: role || 'USER',
          isActive: isActive !== undefined ? isActive : true,
          emailVerified: emailVerified !== undefined ? emailVerified : false,
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          avatar: true,
        },
      });

      return {
        success: true,
        message: 'User created successfully',
        data: newUser,
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update a user
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update a user' })
  @ApiResponse({ status: 200, description: 'User updated successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUser(@Param('id') id: string, @Body() userData: any) {
    try {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!existingUser) {
        return {
          success: false,
          message: 'User not found',
        };
      }

      // Prepare update data
      const updateData: any = {};

      if (userData.name !== undefined) {
        updateData.name = userData.name;
      }

      if (userData.email !== undefined) {
        // Check if email already exists for another user
        const emailExists = await this.prisma.user.findFirst({
          where: {
            email: userData.email,
            id: { not: id },
          },
        });

        if (emailExists) {
          return {
            success: false,
            message: 'Email already exists',
          };
        }

        updateData.email = userData.email;
      }

      if (userData.password !== undefined) {
        updateData.password = await bcrypt.hash(userData.password, 10);
      }

      if (userData.role !== undefined) {
        updateData.role = userData.role;
      }

      if (userData.isActive !== undefined) {
        updateData.isActive = userData.isActive;
      }

      if (userData.emailVerified !== undefined) {
        updateData.emailVerified = userData.emailVerified;
      }

      if (userData.avatar !== undefined) {
        updateData.avatar = userData.avatar;
      }

      // Update user
      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          avatar: true,
        },
      });

      return {
        success: true,
        message: 'User updated successfully',
        data: updatedUser,
      };
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete a user
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a user' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async deleteUser(@Param('id') id: string) {
    try {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!existingUser) {
        return {
          success: false,
          message: 'User not found',
        };
      }

      // Delete user
      await this.prisma.user.delete({
        where: { id },
      });

      return {
        success: true,
        message: 'User deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Get user statistics
   */
  @Get('stats/overview')
  @ApiOperation({ summary: 'Get user statistics' })
  @ApiResponse({ status: 200, description: 'User statistics' })
  async getUserStats() {
    try {
      // Get total users
      const totalUsers = await this.prisma.user.count();

      // Get active users
      const activeUsers = await this.prisma.user.count({
        where: {
          isActive: true,
        },
      });

      // Get new users (last 30 days)
      const newUsers = await this.prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
      });

      // Get users by role
      const roles = ['USER', 'ADMIN', 'SUPER_ADMIN'];

      const usersByRole = await Promise.all(
        roles.map(async (role) => {
          const count = await this.prisma.user.count({
            where: {
              role: role as any, // Cast to any to avoid TypeScript error
            },
          });

          return {
            role,
            count,
          };
        })
      );

      // Get users with subscriptions
      const usersWithSubscriptions = await this.prisma.user.count({
        where: {
          subscriptions: {
            some: {},
          },
        },
      });

      // Get users with orders
      const usersWithOrders = await this.prisma.user.count({
        where: {
          orders: {
            some: {},
          },
        },
      });

      // Calculate growth rate
      const previousMonthUsers = await this.prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 60)),
            lt: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
      });

      const growthRate = previousMonthUsers > 0
        ? ((newUsers - previousMonthUsers) / previousMonthUsers) * 100
        : 0;

      return {
        totalUsers,
        activeUsers,
        newUsers,
        usersByRole,
        usersWithSubscriptions,
        usersWithOrders,
        growthRate,
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }
}
