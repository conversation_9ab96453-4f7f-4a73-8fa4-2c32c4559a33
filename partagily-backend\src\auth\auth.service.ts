import { Injectable, UnauthorizedException, BadRequestException, ConflictException, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';
import { PrismaService } from '../prisma/prisma.service';
import { RegisterDto } from './dto/register.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private prisma: PrismaService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    console.log(`Validating user with email: ${email}`);

    // Find the user by email
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      console.log(`User with email ${email} not found`);
      return null;
    }

    console.log(`User found: ${user.id}, checking password...`);

    // Verify the password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log('Password validation failed');
      return null;
    }

    console.log('Password validation successful');

    // Return user without password
    const { password: _, ...result } = user;
    return result;
  }

  async register(registerDto: RegisterDto) {
    console.log('Register DTO:', registerDto);

    // Check if passwords match
    if (registerDto.password !== registerDto.passwordConfirm) {
      throw new BadRequestException('Passwords do not match');
    }

    try {
      console.log('Creating user with:', {
        email: registerDto.email,
        name: registerDto.name,
        password: '***REDACTED***',
      });

      // Verify database connection before attempting to create user
      if (!this.prisma.isDatabaseConnected()) {
        console.error('Database is not connected. Cannot register user.');
        throw new InternalServerErrorException('Database connection error. Please try again later.');
      }

      // Create the user - this will throw an error if it fails
      const newUser = await this.usersService.create({
        email: registerDto.email,
        name: registerDto.name,
        password: registerDto.password,
      });

      // Double-check that we have a valid user object
      if (!newUser || !newUser.id) {
        console.error('Invalid user object returned from usersService.create');
        throw new InternalServerErrorException('Failed to create user. Please try again later.');
      }

      console.log('User created successfully:', {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
      });

      // Generate tokens
      const tokens = await this.getTokens(newUser.id, newUser.email);
      console.log('Tokens generated successfully');

      // Create a session
      try {
        await this.createSession(newUser.id, tokens.refreshToken);
        console.log('Session created successfully');
      } catch (sessionError) {
        console.error('Failed to create session:', sessionError);
        // Continue anyway - the user is created and tokens are generated
        // This is not a critical error that should prevent registration
      }

      // Return user info and tokens
      const { password, ...userInfo } = newUser;
      return {
        user: userInfo,
        ...tokens,
      };
    } catch (error) {
      console.error('Error during registration:', error);

      // Handle specific error types
      if (error instanceof ConflictException) {
        // Email already in use
        console.log('Registration failed: Email already in use');
        throw error;
      } else if (error instanceof InternalServerErrorException) {
        // Already formatted error
        console.log('Registration failed: Internal server error');
        throw error;
      } else if (error instanceof BadRequestException) {
        // Already formatted error
        console.log('Registration failed: Bad request');
        throw error;
      } else if (error.message && error.message.includes('Database connection error')) {
        // Database connection error
        console.log('Registration failed: Database connection error');
        throw new InternalServerErrorException('Database connection error. Please try again later.');
      } else if (error.message && error.message.includes('Failed to create user')) {
        // User creation error
        console.log('Registration failed: User creation error');
        throw new BadRequestException(error.message);
      } else if (error.code === 'P2002') {
        // Prisma unique constraint violation
        console.log('Registration failed: Prisma unique constraint violation');
        throw new ConflictException('Email already in use');
      } else {
        // Generic error
        console.error('Unhandled registration error:', error);

        // Always provide a clear error message
        throw new InternalServerErrorException(
          'Registration failed: ' + (error.message || 'Unknown error occurred')
        );
      }
    }
  }

  async login(user: any) {
    console.log('Login for user:', user.email);

    // Generate tokens
    const tokens = await this.getTokens(user.id, user.email);
    console.log('Tokens generated successfully');

    // Create a session
    await this.createSession(user.id, tokens.refreshToken);
    console.log('Session created successfully');

    // Create a clean user object with the correct role
    // Force the role to be lowercase for consistency
    const role = typeof user.role === 'string' ? user.role.toLowerCase() : user.role;

    const userResponse = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: role, // Use the lowercase role
    };

    // Add debug logging
    console.log('Login response user data:', userResponse);

    // Return user info and tokens
    return {
      user: userResponse,
      ...tokens,
    };
  }

  async refreshTokens(userId: string, refreshToken: string) {
    console.log(`Refreshing tokens for user: ${userId}`);

    // Find the session
    const session = await this.prisma.session.findFirst({
      where: {
        userId,
        token: refreshToken,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!session) {
      console.log('Invalid refresh token - session not found or expired');
      throw new UnauthorizedException('Invalid refresh token');
    }

    console.log('Valid session found, getting user details');

    // Get the user
    const user = await this.usersService.findById(userId);
    if (!user) {
      console.log('User not found');
      throw new UnauthorizedException('User not found');
    }

    console.log('User found, generating new tokens');

    // Generate new tokens
    const tokens = await this.getTokens(user.id, user.email);
    console.log('New tokens generated successfully');

    // Update the session
    await this.prisma.session.update({
      where: { id: session.id },
      data: {
        token: tokens.refreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      },
    });
    console.log('Session updated with new refresh token');

    return tokens;
  }

  async logout(userId: string) {
    console.log(`Logging out user: ${userId}`);

    // Define the database operation
    const dbOperation = async () => {
      // Delete all sessions for the user
      const result = await this.prisma.session.deleteMany({
        where: { userId },
      });
      console.log(`Deleted ${result.count} sessions for user: ${userId}`);
      return result;
    };

    // Define the fallback operation (mock data)
    const fallbackOperation = async () => {
      console.log('Using mock data for logout');
      console.log(`Mock: Deleted all sessions for user: ${userId}`);
      return { count: 1 };
    };

    // Execute the operation safely
    const result = await this.prisma.safeExecute(dbOperation, fallbackOperation);

    return { message: 'Logged out successfully' };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const { email } = forgotPasswordDto;

    // Find the user
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      // Don't reveal that the user doesn't exist
      return { message: 'If your email is registered, you will receive a password reset link' };
    }

    // Generate a reset token
    const resetToken = this.jwtService.sign(
      { sub: user.id },
      {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn: '15m', // Short expiration for security
      },
    );

    // In a real application, you would send an email with the reset link
    // For this example, we'll just return the token
    return {
      message: 'If your email is registered, you will receive a password reset link',
      // Only include this in development
      resetToken,
    };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { token, password, passwordConfirm } = resetPasswordDto;

    // Check if passwords match
    if (password !== passwordConfirm) {
      throw new BadRequestException('Passwords do not match');
    }

    try {
      // Verify the token
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      const userId = payload.sub;

      // Update the user's password
      await this.usersService.updatePassword(userId, password);

      return {
        message: 'Password reset successfully',
        userId // Return the user ID for audit logging
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  private async getTokens(userId: string, email: string) {
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        {
          sub: userId,
          email,
        },
        {
          secret: this.configService.get<string>('JWT_SECRET'),
          expiresIn: this.configService.get<string>('JWT_EXPIRES_IN'),
        },
      ),
      this.jwtService.signAsync(
        {
          sub: userId,
          email,
        },
        {
          secret: this.configService.get<string>('JWT_SECRET'),
          expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN'),
        },
      ),
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  private async createSession(userId: string, refreshToken: string) {
    console.log('Creating session for user:', userId);

    // Define the database operation
    const dbOperation = async () => {
      // Delete any existing sessions for the user
      await this.prisma.session.deleteMany({
        where: { userId },
      });
      console.log('Deleted existing sessions for user:', userId);

      // Create a new session
      const session = await this.prisma.session.create({
        data: {
          userId,
          token: refreshToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });
      console.log('Created new session:', session.id);

      return session;
    };

    // Define the fallback operation (mock data)
    const fallbackOperation = async () => {
      console.log('Using mock data for createSession');

      const mockSession = {
        id: uuidv4(),
        userId,
        token: refreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      console.log('Created mock session:', mockSession.id);
      return mockSession;
    };

    // Execute the operation safely
    return await this.prisma.safeExecute(dbOperation, fallbackOperation);
  }
}
