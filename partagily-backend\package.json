{"name": "partagily-backend", "version": "0.1.0", "description": "Partagily Backend API", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "import:tools": "ts-node scripts/import-tools.ts", "import:tools-csv": "ts-node scripts/import-tools-csv.ts"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.4.17", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.17", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.2.0", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^11.0.0", "@prisma/client": "^6.7.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.7", "csv-parse": "^5.6.0", "express-session": "^1.18.1", "helmet": "^7.2.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.2", "typeorm": "^0.3.22", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^10.1.11", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.2.0", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.3", "@types/node": "^20.17.32", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.6.2", "prettier": "^3.0.2", "prisma": "^6.7.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}