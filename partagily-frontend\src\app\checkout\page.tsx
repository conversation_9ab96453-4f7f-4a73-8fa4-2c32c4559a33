'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { ShoppingCart, X, CreditCard, ArrowRight, Trash2 } from 'lucide-react';
import userService from '@/services/userService';
import { useNotification } from '@/contexts/NotificationContext';
import Image from 'next/image';
import '@/styles/checkout.css';

export default function CheckoutPage() {
  const [cartItems, setCartItems] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { showNotification } = useNotification();
  const router = useRouter();

  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      setIsLoading(true);
      console.log('Checkout page - Fetching cart...');

      // Try to get cart from API
      try {
        const response = await userService.getCart();
        console.log('Checkout page - Cart API response:', response);

        if (response?.success && response?.data && Array.isArray(response.data.items)) {
          console.log('Checkout page - Setting cart items from API:', response.data.items);
          setCartItems(response.data.items);
          setError(null);
          return;
        } else {
          console.warn('Checkout page - Invalid API response:', response);
        }
      } catch (apiError) {
        console.error('Checkout page - API error:', apiError);
      }

      // If API fails, try to get cart from localStorage
      try {
        const storedCart = localStorage.getItem('mockCart');
        if (storedCart) {
          const parsedCart = JSON.parse(storedCart);
          console.log('Checkout page - Using localStorage cart:', parsedCart);
          if (parsedCart && Array.isArray(parsedCart.items)) {
            setCartItems(parsedCart.items);
            setError(null);
            return;
          }
        }
      } catch (storageError) {
        console.error('Checkout page - localStorage error:', storageError);
      }

      // If all else fails, use empty cart
      console.log('Checkout page - Using empty cart');
      setCartItems([]);
      setError(null);
    } catch (err: any) {
      console.error('Checkout page - Error fetching cart:', err);
      setError('Failed to load your cart. Please try again later.');
      showNotification('error', 'Failed to load your cart', {
        autoClose: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    try {
      const response = await userService.removeFromCart(itemId);

      if (response?.success && response?.data) {
        setCartItems(response.data.items || []);
        showNotification('success', 'Item removed from cart', {
          autoClose: true,
        });
      }
    } catch (err: any) {
      console.error('Error removing item from cart:', err);
      showNotification('error', err.message || 'Failed to remove item from cart', {
        autoClose: true,
      });
    }
  };

  const handleCheckout = async () => {
    try {
      setIsProcessing(true);
      const response = await userService.checkout();

      if (response?.success) {
        showNotification('success', 'Payment successful! Your order has been processed.', {
          autoClose: true,
        });

        // Redirect to thank you page
        router.push('/checkout/thank-you');
      }
    } catch (err: any) {
      console.error('Error during checkout:', err);
      showNotification('error', err.message || 'Failed to process payment', {
        autoClose: true,
      });
      setIsProcessing(false);
    }
  };

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => total + item.price, 0);
  };

  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center min-h-[80vh] py-20 checkout-container">
        <div className="animate-spin rounded-full h-16 w-16 border-t-3 border-b-3 border-yellow-400 mb-6"></div>
        <p className="text-gray-600 dark:text-gray-400 text-lg">Loading your cart...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-24 min-h-[80vh] checkout-container">
      <div className="mb-16 pt-8 checkout-header">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white flex items-center">
          <ShoppingCart className="mr-4" size={36} />
          Your Cart
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-4 text-lg">
          Review your items and proceed to checkout
        </p>
      </div>

      {error && (
        <div className="mb-10 p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg shadow-md">
          <p className="font-medium text-lg mb-2">Error</p>
          <p className="text-base">{error}</p>
        </div>
      )}

      {cartItems.length === 0 ? (
        <div className="bg-white dark:bg-slate-800 rounded-xl p-12 shadow-md text-center my-12 empty-cart checkout-content">
          <div className="flex justify-center mb-6">
            <ShoppingCart size={80} className="text-gray-400 dark:text-gray-500" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Your cart is empty</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-8 text-lg">
            Looks like you haven't added any items to your cart yet.
          </p>
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-3 px-8 rounded-lg flex items-center justify-center mx-auto transition-colors duration-200 text-lg"
          >
            Browse Tools <ArrowRight size={18} className="ml-2" />
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16 checkout-content">
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-slate-800 rounded-xl shadow-md overflow-hidden">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  Cart Items ({cartItems.length})
                </h2>
              </div>

              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {cartItems.map((item) => (
                  <div key={item.id} className="p-8 flex flex-col sm:flex-row sm:items-center justify-between cart-item">
                    <div className="flex items-start mb-4 sm:mb-0">
                      <div className="w-16 h-16 rounded-lg bg-gray-100 dark:bg-slate-700 flex items-center justify-center mr-5 overflow-hidden">
                        {item.icon ? (
                          <Image
                            src={item.icon}
                            alt={item.name}
                            width={64}
                            height={64}
                            className="object-contain"
                            onError={(e) => {
                              // If image fails to load, replace with fallback
                              (e.target as HTMLImageElement).style.display = 'none';
                              const parent = (e.target as HTMLImageElement).parentElement;
                              if (parent) {
                                const fallback = document.createElement('div');
                                fallback.className = 'text-3xl';
                                fallback.textContent = item.type === 'TOOL' ? '🔧' : '📦';
                                parent.appendChild(fallback);
                              }
                            }}
                          />
                        ) : (
                          <div className="text-3xl">
                            {item.type === 'TOOL' ? '🔧' : '📦'}
                          </div>
                        )}
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white">{item.name}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {item.type === 'TOOL' ? 'Individual Tool' : 'Subscription Plan'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between sm:justify-end w-full sm:w-auto">
                      <span className="text-xl font-bold text-gray-900 dark:text-white mr-8">
                        ${item.price.toFixed(2)}
                      </span>
                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors p-2 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full"
                        aria-label="Remove item"
                      >
                        <Trash2 size={20} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-slate-800 rounded-xl shadow-md overflow-hidden sticky top-8">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  Order Summary
                </h2>
              </div>

              <div className="p-8 order-summary">
                <div className="space-y-5 mb-8">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 text-lg">Subtotal</span>
                    <span className="text-gray-900 dark:text-white text-lg">${calculateTotal().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 text-lg">Tax</span>
                    <span className="text-gray-900 dark:text-white text-lg">$0.00</span>
                  </div>
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-5 mt-2 flex justify-between">
                    <span className="font-bold text-gray-900 dark:text-white text-lg">Total</span>
                    <span className="font-bold text-gray-900 dark:text-white text-xl">${calculateTotal().toFixed(2)}</span>
                  </div>
                </div>

                <button
                  onClick={handleCheckout}
                  disabled={isProcessing}
                  className="w-full bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-4 px-6 rounded-lg flex items-center justify-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-md hover:shadow-lg"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-black mr-3"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard size={20} className="mr-3" />
                      Pay Securely with Konnect
                    </>
                  )}
                </button>

                <div className="mt-8 flex justify-center">
                  <Image
                    src="https://s3.eu-west-3.amazonaws.com/konnect.network.public/logo_konnect_23a791d66b.svg"
                    alt="Konnect Payment"
                    width={140}
                    height={50}
                    className="h-10 w-auto"
                  />
                </div>

                <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-6">
                  Your payment information is processed securely by Konnect. We do not store your payment details.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="checkout-footer"></div>
    </div>
  );
}
