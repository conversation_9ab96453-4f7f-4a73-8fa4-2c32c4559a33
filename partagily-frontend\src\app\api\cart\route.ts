import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the backend URL from environment variables or use default
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005';

    console.log('API route /api/cart - Proxying GET request to backend:', `${backendUrl}/cart`);

    // Extract auth token from request headers
    const authToken = request.headers.get('authorization');

    if (!authToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Forward the request to the backend
    const response = await fetch(`${backendUrl}/cart`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': authToken,
      },
      // Add a timeout
      signal: AbortSignal.timeout(5000),
      cache: 'no-store',
    });

    // If the response is not OK, throw an error
    if (!response.ok) {
      console.error('Backend API returned error:', response.status, response.statusText);

      // Return a more helpful error response
      return NextResponse.json(
        {
          error: 'Backend API error',
          status: response.status,
          message: response.statusText || 'Failed to fetch cart from backend'
        },
        { status: response.status }
      );
    }

    // Parse the response as JSON
    const data = await response.json();

    console.log('Backend API response received, forwarding to client');

    // Return the data
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in /api/cart route:', error.message);

    // Return a helpful error response
    return NextResponse.json(
      {
        error: 'API proxy error',
        message: error.message || 'Failed to proxy request to backend'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the backend URL from environment variables or use default
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005';

    console.log('API route /api/cart - Proxying POST request to backend:', `${backendUrl}/cart/items`);

    // Extract auth token from request headers
    const authToken = request.headers.get('authorization');

    if (!authToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await request.json();

    // Log the request body for debugging
    console.log('Cart API - Request body:', body);

    // Validate the itemId format
    if (!body.itemId) {
      return NextResponse.json(
        {
          error: 'Invalid request',
          message: 'Item ID is required'
        },
        { status: 400 }
      );
    }

    // Forward the request to the backend
    const response = await fetch(`${backendUrl}/cart/items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': authToken,
      },
      body: JSON.stringify(body),
      // Add a timeout
      signal: AbortSignal.timeout(10000), // Increased timeout to 10 seconds
    });

    // If the response is not OK, throw an error
    if (!response.ok) {
      console.error('Backend API returned error:', response.status, response.statusText);

      // Return a more helpful error response
      return NextResponse.json(
        {
          error: 'Backend API error',
          status: response.status,
          message: response.statusText || 'Failed to add item to cart'
        },
        { status: response.status }
      );
    }

    // Parse the response as JSON
    const data = await response.json();

    console.log('Backend API response received, forwarding to client');

    // Return the data
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in /api/cart route:', error.message);

    // Return a helpful error response
    return NextResponse.json(
      {
        error: 'API proxy error',
        message: error.message || 'Failed to proxy request to backend'
      },
      { status: 500 }
    );
  }
}
