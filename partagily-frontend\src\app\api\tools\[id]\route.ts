import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the backend URL from environment variables or use default
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005';
    
    // Get the tool ID from the URL params
    const toolId = params.id;
    
    console.log(`API route /api/tools/${toolId} - Proxying GET request to backend:`, `${backendUrl}/tools/${toolId}`);
    
    // Extract auth token from request headers
    const authToken = request.headers.get('authorization');
    
    // Forward the request to the backend
    const response = await fetch(`${backendUrl}/tools/${toolId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(authToken ? { 'Authorization': authToken } : {}),
      },
      // Add a timeout
      signal: AbortSignal.timeout(5000),
      cache: 'no-store',
    });

    // If the response is not OK, throw an error
    if (!response.ok) {
      console.error('Backend API returned error:', response.status, response.statusText);
      
      // Return a more helpful error response
      return NextResponse.json(
        { 
          error: 'Backend API error', 
          status: response.status,
          message: response.statusText || 'Failed to fetch tool from backend'
        }, 
        { status: response.status }
      );
    }

    // Parse the response as JSON
    const data = await response.json();
    
    console.log('Backend API response received, forwarding to client');
    
    // Return the data
    return NextResponse.json(data);
  } catch (error: any) {
    console.error(`Error in /api/tools/[id] route:`, error.message);
    
    // Return a helpful error response
    return NextResponse.json(
      { 
        error: 'API proxy error', 
        message: error.message || 'Failed to proxy request to backend'
      }, 
      { status: 500 }
    );
  }
}
