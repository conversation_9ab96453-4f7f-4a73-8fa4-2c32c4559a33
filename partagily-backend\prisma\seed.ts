import { PrismaClient, User<PERSON>ole, ToolCategory, ToolStatus, PlanTier } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Clean the database
  await prisma.session.deleteMany();
  await prisma.order.deleteMany();
  await prisma.subscription.deleteMany();
  await prisma.tool.deleteMany();
  await prisma.plan.deleteMany();
  await prisma.user.deleteMany();

  console.log('Database cleaned');

  // Create plans
  const standardPlan = await prisma.plan.create({
    data: {
      name: 'Standard',
      tier: PlanTier.STANDARD,
      price: 9.99,
      description: 'Basic access to shared accounts',
      features: [
        'Access to 10+ premium tools',
        'Basic support',
        '1 concurrent login',
        'Standard availability'
      ]
    }
  });

  const premiumPlan = await prisma.plan.create({
    data: {
      name: 'Premium',
      tier: PlanTier.PREMIUM,
      price: 19.99,
      description: 'Enhanced access with priority support',
      features: [
        'Access to 50+ premium tools',
        'Priority support',
        '2 concurrent logins',
        'High availability',
        'Premium tool selection'
      ]
    }
  });

  const goldPlan = await prisma.plan.create({
    data: {
      name: 'Gold',
      tier: PlanTier.GOLD,
      price: 29.99,
      description: 'Full access to all premium tools',
      features: [
        'Access to 100+ premium tools',
        '24/7 support',
        '3 concurrent logins',
        'Highest availability',
        'All premium tools included',
        'Early access to new tools'
      ]
    }
  });

  console.log('Plans created');

  // Create tools
  const tools = await Promise.all([
    prisma.tool.create({
      data: {
        name: 'Adobe Creative Cloud',
        description: 'Access Photoshop, Illustrator, and more with a shared account.',
        icon: '/tools/adobe.png',
        price: 19.99,
        category: 'design' as any,
        status: ToolStatus.AVAILABLE,
        requiredPlan: PlanTier.PREMIUM
      }
    }),
    prisma.tool.create({
      data: {
        name: 'Microsoft Office 365',
        description: 'Use Word, Excel, PowerPoint and other Office applications.',
        icon: '/tools/office.png',
        price: 9.99,
        category: 'writing' as any, // Changed from productivity
        status: ToolStatus.AVAILABLE,
        requiredPlan: PlanTier.STANDARD
      }
    }),
    prisma.tool.create({
      data: {
        name: 'Canva Pro',
        description: 'Create professional designs with premium Canva features.',
        icon: '/tools/canva.png',
        price: 9.99,
        category: 'design' as any,
        status: ToolStatus.AVAILABLE,
        requiredPlan: PlanTier.STANDARD
      }
    }),
    prisma.tool.create({
      data: {
        name: 'Grammarly Premium',
        description: 'Advanced grammar and writing suggestions for your content.',
        icon: '/tools/grammarly.png',
        price: 9.99,
        category: 'writing' as any,
        status: ToolStatus.AVAILABLE,
        requiredPlan: PlanTier.STANDARD
      }
    }),
    prisma.tool.create({
      data: {
        name: 'ChatGPT Plus',
        description: 'Priority access to OpenAI\'s advanced AI assistant.',
        icon: '/tools/chatgpt.png',
        price: 19.99,
        category: 'ai' as any,
        status: ToolStatus.AVAILABLE,
        requiredPlan: PlanTier.PREMIUM
      }
    }),
    prisma.tool.create({
      data: {
        name: 'Notion Premium',
        description: 'Organize your work and life with advanced Notion features.',
        icon: '/tools/notion.png',
        price: 9.99,
        category: 'writing' as any, // Changed from productivity
        status: ToolStatus.AVAILABLE,
        requiredPlan: PlanTier.STANDARD
      }
    })
  ]);

  console.log('Tools created');

  // Create users
  const adminPassword = await bcrypt.hash('admin123', 10);
  const userPassword = await bcrypt.hash('user123', 10);

  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: adminPassword,
      name: 'Admin User',
      role: UserRole.ADMIN,
      emailVerified: true
    }
  });

  const user = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: userPassword,
      name: 'Test User',
      role: UserRole.USER,
      emailVerified: true
    }
  });

  console.log('Users created');

  // Create subscriptions
  const userSubscription = await prisma.subscription.create({
    data: {
      userId: user.id,
      planId: premiumPlan.id,
      startDate: new Date(),
      endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      status: 'ACTIVE',
      autoRenew: true
    }
  });

  console.log('Subscriptions created');

  // Create orders
  const order = await prisma.order.create({
    data: {
      userId: user.id,
      planId: premiumPlan.id,
      amount: premiumPlan.price,
      paymentMethod: 'Credit Card',
      paymentStatus: 'COMPLETED'
    }
  });

  console.log('Orders created');

  console.log('Database seeded successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
