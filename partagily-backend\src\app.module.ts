import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { ToolsModule } from './tools/tools.module';
import { SubscriptionsModule } from './subscriptions/subscriptions.module';
import { PlansModule } from './plans/plans.module';
import { PrismaModule } from './prisma/prisma.module';
import { SecurityModule } from './common/security.module';
import { AdminModule } from './admin/admin.module';
import { AuditModule } from './audit/audit.module';
import { CartModule } from './cart/cart.module';
import { OrderHistoryModule } from './order-history/order-history.module';
import { HealthModule } from './health/health.module';
import { PaymentModule } from './payment/payment.module';
import { CsrfMiddleware } from './common/middleware/csrf.middleware';
import { RateLimitMiddleware } from './common/middleware/rate-limit.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    SecurityModule,
    PrismaModule,
    AuthModule,
    UsersModule,
    ToolsModule,
    SubscriptionsModule,
    PlansModule,
    AdminModule,
    AuditModule,
    CartModule,
    OrderHistoryModule,
    HealthModule,
    PaymentModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule implements NestModule {
  constructor(
    private readonly csrfMiddleware: CsrfMiddleware,
    private readonly rateLimitMiddleware: RateLimitMiddleware,
  ) {}

  configure(consumer: MiddlewareConsumer) {
    // Apply rate limiting middleware first
    consumer
      .apply(RateLimitMiddleware)
      .forRoutes('*');

    // CSRF middleware temporarily disabled for development
    // consumer
    //   .apply(CsrfMiddleware)
    //   .forRoutes('*');

    console.log('CSRF protection is disabled for development');
  }
}
