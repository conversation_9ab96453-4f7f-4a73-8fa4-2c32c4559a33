import axios from 'axios';
import userStorageService from '@/services/userStorageService';

// Use the correct API URL based on environment
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005';

// Helper function to set auth tokens using cookies
const setAuthTokens = (accessToken: string, refreshToken: string, rememberMe: boolean) => {
  // Calculate expiry - 7 days for remember me, 1 day for session
  const expiryDays = rememberMe ? 7 : 1;
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + expiryDays);

  // Set secure cookies with proper attributes
  document.cookie = `accessToken=${accessToken}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax; ${location.protocol === 'https:' ? 'Secure;' : ''}`;
  document.cookie = `refreshToken=${refreshToken}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax; ${location.protocol === 'https:' ? 'Secure;' : ''}`;

  // Also store in localStorage/sessionStorage as fallback
  if (rememberMe) {
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  } else {
    sessionStorage.setItem('accessToken', accessToken);
    sessionStorage.setItem('refreshToken', refreshToken);
  }

  console.log('Auth tokens set in cookies and storage');
};

// Helper function to get access token from cookies first, then storage
const getAccessToken = (): string | null => {
  // Try to get from cookies first
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'accessToken') {
      return value;
    }
  }

  // Fallback to localStorage/sessionStorage
  return localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken');
};

// Helper function to get refresh token from cookies first, then storage
const getRefreshToken = (): string | null => {
  // Try to get from cookies first
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'refreshToken') {
      return value;
    }
  }

  // Fallback to localStorage/sessionStorage
  return localStorage.getItem('refreshToken') || sessionStorage.getItem('refreshToken');
};

// Helper function to clear auth tokens from both cookies and storage
const clearAuthTokens = () => {
  // Clear cookies by setting expiry in the past
  document.cookie = 'accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax;';
  document.cookie = 'refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax;';

  // Also clear from localStorage/sessionStorage
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('mockUserRole');
  sessionStorage.removeItem('accessToken');
  sessionStorage.removeItem('refreshToken');
  sessionStorage.removeItem('mockUserRole');

  console.log('Auth tokens cleared from cookies and storage');
};

// Helper function to clear all user data (for debugging)
const clearAllUserData = () => {
  // Clear tokens
  clearAuthTokens();

  // Clear user data
  localStorage.removeItem('user');
  sessionStorage.removeItem('user');

  // Clear mock user role
  localStorage.removeItem('mockUserRole');
  sessionStorage.removeItem('mockUserRole');

  console.log('All user data cleared');
};

// Helper function to get CSRF token from cookies
const getCsrfToken = (): string => {
  // First try to get from cookies
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'XSRF-TOKEN') {
      return decodeURIComponent(value);
    }
  }

  // If not found in cookies, return a dummy token for development
  console.log('Using dummy CSRF token for development');
  return 'dummy-csrf-token-for-development';
};

// Create axios instance with interceptors
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Include cookies in requests
  timeout: 10000, // 10 seconds timeout

  // Ensure cookies are sent with every request
  xsrfCookieName: 'XSRF-TOKEN',
  xsrfHeaderName: 'X-CSRF-Token',
});

// Add request interceptor to add auth token and CSRF token
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token for non-GET requests
    if (config.method !== 'get') {
      // Always use the dummy token for development
      config.headers['X-CSRF-Token'] = getCsrfToken();
      console.log('Added CSRF token to request:', config.url);
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // If there's no config, just reject the promise
    if (!error.config) {
      return Promise.reject(new Error('Network error occurred'));
    }

    const originalRequest = error.config;

    // If error is 401 and not already retrying
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();

        if (!refreshToken) {
          // No refresh token, clear auth but don't redirect
          // Let the component handle the redirect
          clearAuthTokens();
          return Promise.reject(new Error('Authentication failed. Please log in again.'));
        }

        // Get CSRF token first
        await fetchCsrfToken();

        // Try to refresh the token
        const response = await axios.post(`${API_URL}/auth/refresh`, {
          refreshToken,
        }, {
          headers: {
            'X-CSRF-Token': getCsrfToken() || '',
          },
          withCredentials: true,
        });

        const { accessToken, refreshToken: newRefreshToken } = response.data;

        // Store the new tokens
        const rememberMe = !!localStorage.getItem('refreshToken');
        setAuthTokens(accessToken, newRefreshToken, rememberMe);

        // Retry the original request with new token
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, clear auth but don't redirect
        // Let the component handle the redirect
        clearAuthTokens();

        // Create a clean error object
        return Promise.reject(new Error('Authentication failed. Please log in again.'));
      }
    }

    // For other errors, create a clean error object with a consistent message
    let errorMessage = 'An error occurred';

    if (error.response?.status === 401) {
      clearAuthTokens(); // Clear tokens on any 401 error
      errorMessage = 'Authentication failed. Please log in again.';
    } else if (error.response?.status === 403) {
      errorMessage = 'You do not have permission to perform this action.';
    } else if (error.response?.status === 400) {
      // For validation errors
      errorMessage = error.response.data?.message || 'Invalid request. Please check your input.';
    } else if (error.response?.status === 429) {
      errorMessage = 'Too many requests. Please try again later.';
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return Promise.reject(new Error(errorMessage));
  }
);

// Fetch CSRF token from the server
const fetchCsrfToken = async () => {
  try {
    // Try to get the CSRF token from the server
    const response = await axios.get(`${API_URL}/auth/csrf-token`, {
      withCredentials: true,
      // Add timeout to prevent long waiting
      timeout: 3000
    });
    console.log('CSRF token fetched successfully:', response.data);
    return getCsrfToken();
  } catch (error) {
    console.warn('Failed to fetch CSRF token:', error);
    // Return dummy token for development
    return 'dummy-csrf-token-for-development';
  }
};

// Function to check if the session is valid and refresh if needed
const checkSession = async () => {
  try {
    const token = getAccessToken();
    if (!token) {
      return false;
    }

    // If token is a mock token, consider it valid
    if (token.startsWith('mock-access-token-')) {
      return true;
    }

    // Try to get the current user to validate the session
    try {
      await api.get('/auth/me');
      return true;
    } catch (apiError) {
      // Only try to refresh if it's an authentication error
      if (apiError.response?.status === 401) {
        try {
          // Try to refresh the token
          await refreshToken();
          return true;
        } catch (refreshError) {
          // Only clear tokens if refresh explicitly fails
          // Don't clear tokens for network errors
          if (refreshError.message?.includes('Authentication failed')) {
            clearAuthTokens();
          }
          return false;
        }
      }

      // For other errors (like network errors), assume the session is still valid
      // This prevents logging users out due to temporary network issues
      return true;
    }
  } catch (error) {
    // For unexpected errors, don't log the user out
    console.error('Error checking session:', error);
    return true;
  }
};

// Helper function to reset a user's password (for debugging)
const resetUserPassword = (email: string, newPassword: string) => {
  try {
    // Get registered users
    const registeredUsersStr = localStorage.getItem('registeredUsers');
    if (!registeredUsersStr) {
      console.error('No registered users found');
      return false;
    }

    const registeredUsers = JSON.parse(registeredUsersStr);
    if (!Array.isArray(registeredUsers)) {
      console.error('Invalid registered users data');
      return false;
    }

    // Find the user
    const userIndex = registeredUsers.findIndex((user: any) =>
      user && user.email && user.email.toLowerCase() === email.toLowerCase()
    );

    if (userIndex === -1) {
      console.error('User not found:', email);
      return false;
    }

    // Update the password
    registeredUsers[userIndex].password = newPassword;

    // Save back to localStorage
    localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));

    console.log('Password reset for user:', email);
    console.log('New password:', newPassword);

    return true;
  } catch (e) {
    console.error('Error resetting password:', e);
    return false;
  }
};

const authService = {
  // Check if session is valid
  checkSession,

  // Debug helpers
  clearAllUserData,
  resetUserPassword,

  // Auth token management
  setAuthTokens,
  getAccessToken,
  getRefreshToken,
  clearAuthTokens,

  // Register a new user
  async register(name: string, email: string, password: string, passwordConfirm: string) {
    // Validate input
    if (!name || !email || !password || !passwordConfirm) {
      throw new Error('All fields are required');
    }

    // Validate password match
    if (password !== passwordConfirm) {
      throw new Error('Passwords do not match');
    }

    console.log('Attempting to register with email:', email);
    console.log('Using direct connection to backend server');

    // Try to call the backend API directly
    try {
      // Make a direct request to the backend server
      const directResponse = await fetch('http://localhost:3005/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Origin': window.location.origin,
        },
        body: JSON.stringify({
          name,
          email,
          password,
          passwordConfirm
        }),
        credentials: 'include',
        mode: 'cors',
      });

      if (!directResponse.ok) {
        let errorData;
        try {
          errorData = await directResponse.json();
        } catch (e) {
          errorData = { message: 'Unknown error occurred' };
        }
        console.error('Registration failed with status:', directResponse.status, errorData);
        throw new Error(errorData.message || 'Registration failed');
      }

      const data = await directResponse.json();
      console.log('Registration response received:', directResponse.status);
      console.log('Registration data:', data);

      // Verify we have a valid response
      if (!data || !data.user || !data.user.id) {
        console.error('Invalid response data from registration API:', data);
        throw new Error('Invalid response from server');
      }

      const { user, accessToken, refreshToken } = data;

      // Store tokens
      setAuthTokens(accessToken, refreshToken, true);
      console.log('Auth tokens stored after registration');

      // Store the user in localStorage for future login (without password)
      userStorageService.addUser({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role || 'user',
        createdAt: user.createdAt || new Date().toISOString(),
        updatedAt: user.updatedAt || new Date().toISOString()
      });
      console.log('User stored in local storage after registration');

      // Return the complete response data
      return data;
    } catch (error: any) {
      console.error('Registration failed:', error);

      // Extract error message
      let errorMessage = 'Registration failed. Please try again.';

      if (error.message) {
        errorMessage = error.message;
      }

      console.error('Registration error:', errorMessage);
      throw new Error(errorMessage);
    }
  },

  // Login user
  async login(email: string, password: string, rememberMe: boolean) {
    try {
      // userStorageService is already imported at the top of the file

      // Try to call the API first using direct fetch
      try {
        // Make a direct request to the backend server
        const directResponse = await fetch('http://localhost:3005/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': window.location.origin,
          },
          body: JSON.stringify({
            email,
            password
          }),
          credentials: 'include',
          mode: 'cors',
        });

        if (!directResponse.ok) {
          let errorData;
          try {
            errorData = await directResponse.json();
          } catch (e) {
            errorData = { message: 'Unknown error occurred' };
          }
          console.error('Login failed with status:', directResponse.status, errorData);
          throw new Error(errorData.message || 'Login failed');
        }

        const data = await directResponse.json();
        console.log('Login response received:', directResponse.status);
        console.log('Login data:', data);

        const { user, accessToken, refreshToken } = data;

        // Store tokens
        setAuthTokens(accessToken, refreshToken, rememberMe);

        // Ensure the role is lowercase for consistency
        const normalizedUser = {
          ...user,
          role: typeof user.role === 'string' ? user.role.toLowerCase() : user.role
        };

        // Store the user in localStorage (without password)
        userStorageService.saveCurrentUser({
          id: normalizedUser.id,
          email: normalizedUser.email,
          name: normalizedUser.name,
          role: normalizedUser.role
        });

        return normalizedUser;
      } catch (apiError) {
        console.log('API login failed, using local storage:', apiError);

        // Verify user credentials using userStorageService
        const verifiedUser = userStorageService.verifyUserCredentials(email, password);

        if (!verifiedUser) {
          console.error('Invalid email or password');
          throw new Error('Invalid email or password');
        }

        console.log('User verified:', verifiedUser);

        // Generate mock tokens
        const mockAccessToken = 'mock-access-token-' + Date.now();
        const mockRefreshToken = 'mock-refresh-token-' + Date.now();

        // Store tokens
        setAuthTokens(mockAccessToken, mockRefreshToken, rememberMe);

        // Store user role for mock authentication
        if (rememberMe) {
          localStorage.setItem('mockUserRole', verifiedUser.role);
        } else {
          sessionStorage.setItem('mockUserRole', verifiedUser.role);
        }

        // Store the user in localStorage
        userStorageService.saveCurrentUser(verifiedUser);

        // Create a user object for the frontend
        const mockUser = {
          id: verifiedUser.id,
          email: verifiedUser.email,
          name: verifiedUser.name,
          role: verifiedUser.role,
          isActive: true,
          emailVerified: verifiedUser.email.includes('@partagily.com'), // Built-in users are verified
          createdAt: verifiedUser.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        return mockUser;
      }
    } catch (error: any) {
      // Create a more descriptive error message
      let errorMessage = 'Login failed';

      if (error.response?.status === 401) {
        errorMessage = 'Invalid email or password';
      } else if (error.response?.status === 429) {
        errorMessage = 'Too many login attempts. Please try again later';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Throw a clean error with a consistent message
      throw new Error(errorMessage);
    }
  },

  // Logout user
  async logout() {
    try {
      // userStorageService is already imported at the top of the file

      const token = getAccessToken();
      if (token) {
        // Get CSRF token first
        await fetchCsrfToken();

        try {
          // Call API to logout using direct fetch
          console.log('Calling logout API endpoint using direct fetch');
          const directResponse = await fetch('http://localhost:3005/auth/logout', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${token}`,
              'X-CSRF-Token': getCsrfToken() || '',
              'Origin': window.location.origin,
            },
            credentials: 'include',
            mode: 'cors',
          });

          if (directResponse.ok) {
            console.log('Logout API call successful');
          } else {
            console.error('Logout API call failed with status:', directResponse.status);
          }
        } catch (apiError) {
          console.error('Logout API call failed:', apiError);
          // Continue with local logout even if API call fails
        }
      }

      // Clear tokens regardless of API call success
      clearAuthTokens();

      // Clear user data from localStorage
      localStorage.removeItem('user');
      sessionStorage.removeItem('user');

      // Clear mock user role
      localStorage.removeItem('mockUserRole');
      sessionStorage.removeItem('mockUserRole');

      // Clear any other user-related data
      userStorageService.clearAllUserData();

      console.log('All user data cleared, logout complete');
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      // Clear tokens even if API call fails
      clearAuthTokens();
      return false;
    }
  },

  // Get current user
  async getCurrentUser() {
    try {
      const token = getAccessToken();
      if (!token) {
        console.log('No access token found, user is not authenticated');
        return null;
      }

      console.log('Access token found, fetching user data from API using direct fetch');

      try {
        // Call the API to get the current user using direct fetch
        const directResponse = await fetch('http://localhost:3005/auth/me', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`,
            'Origin': window.location.origin,
          },
          credentials: 'include',
          mode: 'cors',
        });

        if (!directResponse.ok) {
          let errorData;
          try {
            errorData = await directResponse.json();
          } catch (e) {
            errorData = { message: 'Unknown error occurred' };
          }
          console.error('Get current user failed with status:', directResponse.status, errorData);

          // If unauthorized, clear tokens
          if (directResponse.status === 401) {
            console.log('Unauthorized error, clearing tokens');
            clearAuthTokens();
          }

          return null;
        }

        const data = await directResponse.json();
        const user = data.user;
        console.log('User data retrieved from API:', user);

        // Ensure the role is lowercase for consistency
        const normalizedUser = {
          ...user,
          role: typeof user.role === 'string' ? user.role.toLowerCase() : user.role
        };

        console.log('Normalized user data:', normalizedUser);
        return normalizedUser;
      } catch (apiError) {
        console.error('API getCurrentUser failed:', apiError);

        // Clear tokens on error
        clearAuthTokens();
        return null;
      }
    } catch (error: any) {
      console.error('Get current user error:', error);
      // Clear tokens on error
      clearAuthTokens();
      return null;
    }
  },

  // Request password reset
  async forgotPassword(email: string) {
    try {
      // Get CSRF token first
      await fetchCsrfToken();

      // Call API to request password reset
      const response = await api.post('/auth/forgot-password', { email });
      return response.data;
    } catch (error: any) {
      console.error('Forgot password error:', error);
      throw new Error(error.response?.data?.message || error.message || 'Failed to send password reset email');
    }
  },

  // Reset password
  async resetPassword(token: string, password: string, passwordConfirm: string) {
    try {
      // Validate password match
      if (password !== passwordConfirm) {
        throw new Error('Passwords do not match');
      }

      // Get CSRF token first
      await fetchCsrfToken();

      // Call API to reset password
      const response = await api.post('/auth/reset-password', {
        token,
        password,
        passwordConfirm
      });

      return response.data;
    } catch (error: any) {
      console.error('Reset password error:', error);
      throw new Error(error.response?.data?.message || error.message || 'Password reset failed');
    }
  },

  // Check if user is authenticated
  isAuthenticated() {
    return !!getAccessToken();
  },

  // Get access token (for other services)
  getAccessToken,

  // Get refresh token
  getRefreshToken,

  // Get CSRF token
  getCsrfToken,

  // Fetch CSRF token
  fetchCsrfToken,

  // Refresh token
  async refreshToken() {
    try {
      const refreshToken = getRefreshToken();

      if (!refreshToken) {
        console.log('No refresh token available');
        throw new Error('No refresh token available');
      }

      console.log('Refreshing token');

      // Get CSRF token first
      await fetchCsrfToken();

      // Try to refresh the token
      const response = await axios.post(`${API_URL}/auth/refresh`, {
        refreshToken
      }, {
        headers: {
          'X-CSRF-Token': getCsrfToken() || '',
        },
        withCredentials: true,
      });

      console.log('Token refresh successful');
      const { accessToken, refreshToken: newRefreshToken } = response.data;

      // Store the new tokens
      const rememberMe = !!localStorage.getItem('refreshToken');
      setAuthTokens(accessToken, newRefreshToken, rememberMe);
      console.log('New tokens stored');

      return { accessToken, refreshToken: newRefreshToken };
    } catch (error: any) {
      console.error('Token refresh failed:', error);
      // Clear tokens on refresh failure
      clearAuthTokens();
      throw new Error('Authentication failed. Please log in again.');
    }
  },

  // Change password
  async changePassword(currentPassword: string, newPassword: string, confirmPassword: string) {
    try {
      console.log('Changing password');

      // Validate password match
      if (newPassword !== confirmPassword) {
        throw new Error('New passwords do not match');
      }

      // Validate password length
      if (newPassword.length < 8) {
        throw new Error('New password must be at least 8 characters long');
      }

      // Get CSRF token first
      await fetchCsrfToken();

      // Get the access token
      const token = getAccessToken();
      if (!token) {
        throw new Error('You must be logged in to change your password');
      }

      console.log('Calling API to change password');

      // Call API to change password
      const response = await api.post('/users/change-password', {
        currentPassword,
        newPassword,
        confirmPassword
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-CSRF-Token': getCsrfToken() || '',
        }
      });

      console.log('Password changed successfully');
      return response.data;
    } catch (error: any) {
      console.error('Change password error:', error);

      // Create a clean error message
      let errorMessage = 'Failed to change password';

      if (error.response?.status === 401) {
        errorMessage = 'Current password is incorrect';
      } else if (error.response?.status === 400) {
        errorMessage = error.response.data?.message || 'Invalid password format';
      } else if (error.message?.includes('New password must be')) {
        errorMessage = error.message;
      } else if (error.message?.includes('New passwords do not match')) {
        errorMessage = error.message;
      } else if (error.message?.includes('Network Error')) {
        errorMessage = 'Unable to connect to the server. Please check your internet connection.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  },
};

export default authService;
