generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String             @id @default(uuid())
  email             String             @unique
  password          String
  name              String
  avatar            String?
  role              UserRole           @default(USER)
  isActive          Boolean            @default(true)
  emailVerified     Boolean            @default(false)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  carts             Cart[]
  orderHistory      OrderHistory[]
  orders            Order[]
  sessions          Session[]
  subscriptions     Subscription[]
  paymentReferences PaymentReference[]

  @@map("users")
}

model Tool {
  id            String       @id @default(uuid())
  name          String
  description   String
  icon          String?
  price         Float
  category      ToolCategory
  status        ToolStatus   @default(AVAILABLE)
  requiredPlan  PlanTier
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  domain        String?
  originalPrice Float?
  cookies       Cookie[]
  orders        Order[]
  plans         PlanTool[]

  @@map("tools")
}

model Subscription {
  id        String             @id @default(uuid())
  userId    String
  planId    String
  startDate DateTime
  endDate   DateTime
  status    SubscriptionStatus @default(ACTIVE)
  autoRenew Boolean            @default(true)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  plan      Plan               @relation(fields: [planId], references: [id])
  user      User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model Plan {
  id            String         @id @default(uuid())
  name          String
  tier          PlanTier
  price         Float
  description   String
  features      String[]
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  orders        Order[]
  includedTools PlanTool[]
  subscriptions Subscription[]

  @@map("plans")
}

model PlanTool {
  id        String   @id @default(uuid())
  planId    String
  toolId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  plan      Plan     @relation(fields: [planId], references: [id], onDelete: Cascade)
  tool      Tool     @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([planId, toolId])
  @@map("plan_tools")
}

model Order {
  id            String        @id @default(uuid())
  userId        String
  amount        Float
  paymentMethod String
  paymentStatus PaymentStatus @default(PENDING)
  planId        String?
  toolId        String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  plan          Plan?         @relation(fields: [planId], references: [id])
  tool          Tool?         @relation(fields: [toolId], references: [id])
  user          User          @relation(fields: [userId], references: [id])

  @@map("orders")
}

model Session {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model AuditLog {
  id        String   @id @default(uuid())
  eventType String
  userId    String?
  ipAddress String?
  userAgent String?
  severity  String
  details   String
  createdAt DateTime @default(now())

  @@index([eventType])
  @@index([userId])
  @@index([createdAt])
  @@map("audit_logs")
}

model Setting {
  key       String   @id
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

model Cookie {
  id         String   @id @default(uuid())
  toolId     String
  domain     String
  name       String
  value      String
  path       String   @default("/")
  expiresAt  DateTime
  isSecure   Boolean  @default(true)
  isHttpOnly Boolean  @default(false)
  sameSite   String   @default("lax")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  tool       Tool     @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@index([toolId])
  @@map("cookies")
}

model Cart {
  id                String             @id @default(uuid())
  userId            String
  status            CartStatus         @default(OPEN)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  items             CartItem[]
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  paymentReferences PaymentReference[]

  @@map("carts")
}

model CartItem {
  id        String   @id @default(uuid())
  cartId    String
  type      ItemType
  itemId    String
  name      String
  price     Float
  icon      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  cart      Cart     @relation(fields: [cartId], references: [id], onDelete: Cascade)

  @@map("cart_items")
}

model OrderHistory {
  id            String      @id @default(uuid())
  userId        String
  orderNumber   String      @unique
  totalAmount   Float
  status        OrderStatus @default(PENDING)
  paymentMethod String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  items         OrderItem[]

  @@map("order_history")
}

model OrderItem {
  id             String       @id @default(uuid())
  orderHistoryId String
  type           ItemType
  itemId         String
  name           String
  price          Float
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  icon           String?
  orderHistory   OrderHistory @relation(fields: [orderHistoryId], references: [id], onDelete: Cascade)

  @@map("order_items")
}

model PaymentReference {
  id              String   @id @default(uuid())
  userId          String
  cartId          String
  clientReference String   @unique
  amount          Float
  status          String   // PENDING, COMPLETED, FAILED
  paymentRef      String?  @unique
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  cart            Cart     @relation(fields: [cartId], references: [id], onDelete: Cascade)

  @@map("payment_references")
}

enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum ToolCategory {
  streaming
  stock
  publishing
  design
  video
  ai
  music
  ecommerce
  writing
  networking
}

enum ToolStatus {
  AVAILABLE
  UNAVAILABLE
  MAINTENANCE
  COMING_SOON
}

enum PlanTier {
  STANDARD
  PREMIUM
  GOLD
}

enum SubscriptionStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  PENDING
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum CartStatus {
  OPEN
  CHECKED_OUT
}

enum ItemType {
  TOOL
  PLAN
}

enum OrderStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}
