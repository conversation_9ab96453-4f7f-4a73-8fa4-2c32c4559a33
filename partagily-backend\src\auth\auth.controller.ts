import { Controller, Post, UseGuards, Request, Body, Get, Res, Ip } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtRefreshAuthGuard } from './guards/jwt-refresh-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { AuditService, AuditEventType } from '../common/services/audit.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private auditService: AuditService
  ) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'Email already in use' })
  @ApiBody({ type: RegisterDto })
  async register(@Body() registerDto: RegisterDto, @Ip() ip: string, @Request() req) {
    const result = await this.authService.register(registerDto);

    // Audit log for registration
    await this.auditService.logAuthEvent(
      AuditEventType.AUTH_REGISTER,
      result.user.id,
      ip,
      req.headers['user-agent'] || 'unknown',
      { email: registerDto.email }
    );

    return result;
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiBody({ type: LoginDto })
  async login(@Request() req, @Ip() ip: string) {
    const result = await this.authService.login(req.user);

    // Audit log for successful login
    await this.auditService.logAuthEvent(
      AuditEventType.AUTH_LOGIN_SUCCESS,
      req.user.id,
      ip,
      req.headers['user-agent'] || 'unknown',
      { email: req.user.email }
    );

    return result;
  }

  @UseGuards(JwtRefreshAuthGuard)
  @Post('refresh')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async refreshTokens(@Request() req, @Ip() ip: string) {
    const userId = req.user.id;
    const refreshToken = req.user.refreshToken;

    const result = await this.authService.refreshTokens(userId, refreshToken);

    // Audit log for token refresh
    await this.auditService.logAuthEvent(
      AuditEventType.AUTH_TOKEN_REFRESH,
      userId,
      ip,
      req.headers['user-agent'] || 'unknown',
      {}
    );

    return result;
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async logout(@Request() req, @Ip() ip: string) {
    const result = await this.authService.logout(req.user.id);

    // Audit log for logout
    await this.auditService.logAuthEvent(
      AuditEventType.AUTH_LOGOUT,
      req.user.id,
      ip,
      req.headers['user-agent'] || 'unknown',
      {}
    );

    return result;
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  @ApiBody({ type: ForgotPasswordDto })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto, @Ip() ip: string, @Request() req) {
    const result = await this.authService.forgotPassword(forgotPasswordDto);

    // Audit log for password reset request
    await this.auditService.logAuthEvent(
      AuditEventType.AUTH_PASSWORD_RESET_REQUEST,
      null, // User ID might not be known at this point
      ip,
      req.headers['user-agent'] || 'unknown',
      { email: forgotPasswordDto.email }
    );

    return result;
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Password reset successful' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Invalid or expired token' })
  @ApiBody({ type: ResetPasswordDto })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto, @Ip() ip: string, @Request() req) {
    const result = await this.authService.resetPassword(resetPasswordDto);

    // Audit log for password reset
    await this.auditService.logAuthEvent(
      AuditEventType.AUTH_PASSWORD_RESET,
      result.userId, // The service should return the user ID
      ip,
      req.headers['user-agent'] || 'unknown',
      {}
    );

    return result;
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'User profile' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProfile(@Request() req) {
    console.log('User in request:', req.user);
    return { user: req.user };
  }

  @Get('csrf-token')
  @ApiOperation({ summary: 'Get CSRF token' })
  @ApiResponse({ status: 200, description: 'CSRF token' })
  async getCsrfToken(@Request() req, @Res({ passthrough: true }) res, @Ip() ip: string) {
    // Generate a dummy token for development
    const token = 'dummy-csrf-token-for-development';

    // Set the token in a cookie
    res.cookie('XSRF-TOKEN', token, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
    });

    // If user is authenticated, log the CSRF token generation
    if (req.user) {
      await this.auditService.logAuthEvent(
        AuditEventType.AUTH_TOKEN_REFRESH,
        req.user.id,
        ip,
        req.headers['user-agent'] || 'unknown',
        { tokenType: 'CSRF' }
      );
    }

    console.log('Dummy CSRF token generated for development');
    return { message: 'CSRF token generated', token };
  }
}
